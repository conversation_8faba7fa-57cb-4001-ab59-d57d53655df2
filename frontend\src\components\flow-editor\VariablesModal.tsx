import React from 'react'
import { RpaFlow, FlowExecution, RpaStep } from '@rpa-project/shared'

interface VariablesModalProps {
  isOpen: boolean
  onClose: () => void
  flow: RpaFlow
  currentExecution: FlowExecution | null
}

export function VariablesModal({ isOpen, onClose, flow, currentExecution }: VariablesModalProps) {
  const getVariablesFromLogs = (execution: FlowExecution | null): Record<string, any> => {
    if (!execution || !execution.logs) return {}
    
    const variables: Record<string, any> = {}
    
    // Extract variables from log data
    execution.logs.forEach(log => {
      if (log.data && typeof log.data === 'object') {
        Object.assign(variables, log.data)
      }
    })
    
    return variables
  }

  const getExpectedVariables = (): Record<string, string> => {
    if (!flow) return {}
    
    const expectedVars: Record<string, string> = {}
    
    // Find steps that create variables
    flow.steps.forEach(step => {
      if (step.type === 'extractText' && (step as any).variableName) {
        expectedVars[(step as any).variableName] = `Text från: ${(step as any).selector}`
      }
      if (step.type === 'extractAttribute' && (step as any).variableName) {
        expectedVars[(step as any).variableName] = `Attribut ${(step as any).attribute} från: ${(step as any).selector}`
      }
    })
    
    return expectedVars
  }

  if (!isOpen) return null

  return (
    <div className="modal-overlay" onClick={onClose}>
      <div
        className="modal-content"
        style={{
          width: '1000px',
          maxWidth: '90vw',
          height: '90vh',
          maxHeight: '90vh',
          margin: '5vh auto',
          display: 'flex',
          flexDirection: 'column',
          overflow: 'hidden',
          backgroundColor: '#fbf9f8'
        }}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="dashboard-header" style={{
          flexShrink: 0,
          borderBottom: '1px solid #e5e7eb',
          margin: 0,
          backgroundColor: '#fbf9f8'
        }}>
          <div className="dashboard-header-content">
            <p className="dashboard-title" style={{ fontSize: '1.5rem' }}>
              🔧 Flödesvariabler
            </p>
            <p className="dashboard-subtitle">
              Variabler från flödessteg och senaste körning
            </p>
          </div>
          <div style={{ display: 'flex', gap: '0.5rem', alignItems: 'center' }}>
            <button
              onClick={onClose}
              className="action-button secondary"
            >
              <span>✕ Stäng</span>
            </button>
          </div>
        </div>

        {/* Content */}
        <div style={{
          flex: 1,
          overflow: 'hidden',
          display: 'flex',
          flexDirection: 'column',
          backgroundColor: '#ffffff'
        }}>
          <div style={{ flex: 1, overflow: 'auto', padding: '1.5rem' }}>
            {/* Expected Variables Section */}
            <div style={{ marginBottom: '2rem' }}>
              <h3 style={{
                fontSize: '1.125rem',
                fontWeight: '600',
                marginBottom: '1rem',
                color: '#1a0f0f'
              }}>
                Förväntade variabler (från flödessteg)
              </h3>
              <div style={{
                backgroundColor: '#f0f9ff',
                border: '1px solid #0ea5e9',
                borderRadius: '0.5rem',
                padding: '1rem'
              }}>
                {(() => {
                  const expectedVars = getExpectedVariables()
                  return Object.keys(expectedVars).length > 0 ? (
                    <div style={{ display: 'grid', gap: '0.75rem' }}>
                      {Object.entries(expectedVars).map(([key, description]) => (
                        <div key={key} style={{
                          display: 'grid',
                          gridTemplateColumns: '1fr 2fr',
                          gap: '1rem',
                          padding: '0.75rem',
                          backgroundColor: '#ffffff',
                          border: '1px solid #0ea5e9',
                          borderRadius: '0.375rem'
                        }}>
                          <div style={{
                            fontWeight: '500',
                            color: '#0c4a6e',
                            fontFamily: 'monospace'
                          }}>
                            {key}
                          </div>
                          <div style={{
                            color: '#0369a1',
                            fontSize: '0.875rem'
                          }}>
                            {description}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div style={{
                      textAlign: 'center',
                      color: '#0369a1',
                      fontStyle: 'italic',
                      padding: '2rem'
                    }}>
                      Inga steg som skapar variabler hittades
                    </div>
                  )
                })()}
              </div>
            </div>

            {/* Runtime Variables Section */}
            <div>
              <h3 style={{ 
                fontSize: '1.125rem', 
                fontWeight: '600', 
                marginBottom: '1rem',
                color: '#1a0f0f'
              }}>
                Runtime-variabler (från senaste körning)
              </h3>
              <div style={{
                backgroundColor: '#f9fafb',
                border: '1px solid #e5e7eb',
                borderRadius: '0.5rem',
                padding: '1rem'
              }}>
                {(() => {
                  const runtimeVariables = {
                    ...(currentExecution?.results || {}),
                    ...getVariablesFromLogs(currentExecution)
                  }
                  return Object.keys(runtimeVariables).length > 0 ? (
                    <div style={{ display: 'grid', gap: '0.75rem' }}>
                      {Object.entries(runtimeVariables).map(([key, value]) => (
                        <div key={key} style={{
                          display: 'grid',
                          gridTemplateColumns: '1fr 2fr',
                          gap: '1rem',
                          padding: '0.75rem',
                          backgroundColor: '#ffffff',
                          border: '1px solid #e5e7eb',
                          borderRadius: '0.375rem'
                        }}>
                          <div style={{
                            fontWeight: '500',
                            color: '#374151',
                            fontFamily: 'monospace'
                          }}>
                            {key}
                          </div>
                          <div style={{
                            color: '#6b7280',
                            fontFamily: 'monospace',
                            fontSize: '0.875rem',
                            wordBreak: 'break-all'
                          }}>
                            {typeof value === 'object' ? JSON.stringify(value, null, 2) : String(value)}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div style={{
                      textAlign: 'center',
                      color: '#6b7280',
                      fontStyle: 'italic',
                      padding: '2rem'
                    }}>
                      {currentExecution ? 'Inga runtime-variabler från senaste körning' : 'Ingen körning tillgänglig - kör flödet för att se runtime-variabler'}
                    </div>
                  )
                })()}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
